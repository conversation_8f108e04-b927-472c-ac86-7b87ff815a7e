package com.example.pdf.ui.feature.document_picker

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.example.pdf.android.file.ImportDocumentFilesHelper
import java.io.File
import org.koin.compose.koinInject
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.kermit.debugLog
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts

/**
 * A container composable for the document picker functionality that allows selecting PDF and Office documents.
 *
 * @param onPickStarted Called when document picking starts
 * @param onPickComplete Called when document selection completes successfully with the imported file
 * @param onPickFailed Called when document selection fails with an error
 * @param modifier Modifier to be applied to the container
 * @param documentMimeTypes Array of MIME types for documents that can be selected
 * @param importDocumentFilesHelper Helper for importing document files
 * @param content Composable content to be displayed as the trigger for document picking
 */
@Composable
fun DocumentFilePickerContainer(
  onPickStarted: () -> Unit = {},
  onPickComplete: (File) -> Unit = {},
  onPickFailed: () -> Unit = {},
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  documentMimeTypes: Array<String> = ImportDocumentFilesHelper.DOCUMENT_MIME_TYPES,
  importDocumentFilesHelper: ImportDocumentFilesHelper = koinInject(),
  content: @Composable (startPicker: () -> Unit) -> Unit
) {

  // Create document picker launcher using rememberLauncherForActivityResult
  val documentPickerLauncher = rememberLauncherForActivityResult(
    contract = ActivityResultContracts.OpenDocument()
  ) { uri ->
    if (uri == null) {
      onPickFailed()
    } else {
      importDocumentFilesHelper.handleSelectedDocument(uri) { success, file ->
        if (success && file != null) {
          onPickComplete(file)
        } else {
          onPickFailed()
        }
      }
    }
  }

  // Function to start the document picker that will be passed to the content
  val startPicker = remember {
    {
      onPickStarted()
      documentPickerLauncher.launch(documentMimeTypes)
    }
  }

  Box(
    modifier = modifier,
    contentAlignment = Alignment.Center
  ) {
    // Pass the startPicker function to the content
    content(startPicker)
  }
}

/**
 * Usage example of DocumentPickerContainer.
 */
@Composable
private fun DocumentPickerExample(
  onDocumentSelected: (File) -> Unit,
  onPickFailed: () -> Unit = {}
) {
  // Use the DocumentPickerContainer with a Card content
  DocumentFilePickerContainer(
    documentMimeTypes = ImportDocumentFilesHelper.DOCUMENT_MIME_TYPES,
    onPickComplete = onDocumentSelected,
    onPickFailed = onPickFailed,
    modifier = Modifier.padding(16.dp)
  ) { startPicker ->
    Card(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp),
    ) {
      Column(
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        Text(
          text = LocalStrings.current.importDocument,
          modifier = Modifier.padding(bottom = 8.dp)
        )

        Button(
          onClick = startPicker
        ) {
          Text(LocalStrings.current.selectFile)
        }
      }
    }
  }
}

@Preview
@Composable
fun DocumentPickerExamplePreview() {
  AppTheme {
    DocumentPickerExample(onDocumentSelected = {
      debugLog("Document selected: ${it.name}")
    })
  }
}