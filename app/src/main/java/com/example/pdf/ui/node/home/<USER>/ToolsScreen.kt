package com.example.pdf.ui.node.home.tools

import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.R
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.biz.skipSplash
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.feature.document_picker.DocumentFilePickerContainer
import com.example.pdf.ui.feature.document_picker.DocumentUriPickerContainer
import com.example.pdf.ui.feature.document_scanner.DocumentScannerContainer
import com.example.pdf.ui.feature.image_picker.AdaptiveImageUriPickerContainer
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.node.document.pdf.PdfViewerInitEditMode
import com.example.pdf.ui.node.document.pdf.PdfViewerNode
import com.example.pdf.ui.node.img2pdf.list.Img2PdfListNode
import com.example.pdf.ui.node.pdf_picker.AddSignatureToPdfPickerNode
import com.example.pdf.ui.node.pdf_picker.AnnotatePdfPickerNode
import com.example.pdf.ui.node.pdf_picker.LockPdfPickerNode
import com.example.pdf.ui.node.pdf_picker.PdfPickerListType
import com.example.pdf.ui.node.pdf_picker.PdfPickerNode
import com.example.pdf.ui.node.pdf_picker.UnlockPdfPickerNode
import com.example.pdf.ui.node.settings.SettingsItem
import com.example.pdf.ui.node.settings.SettingsNode
import com.example.pdf.ui.node.word2pdf.WordToPdfNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.replaceLast
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Composable
fun ToolsScreen(onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction) {
  val navigator = requireLocalNavigator()

  val uiModel: ToolsUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  if (uiState.processing) {
    LoadingDialog()
  }

  ToolsContent(
    onImportFilePick = {
      DocumentNode(it)?.let(navigator::push)
    },
    onWordToPdfPick = {
      navigator.push(WordToPdfNode(it))
    },
    onScanToPdfComplete = uiModel::onScanPdfComplete,
    onImagesPickComplete = {
      if (it.isNotEmpty()) {
        navigator.push(Img2PdfListNode(it))
      }
    },
    onSettingsClick = { navigator.push(SettingsNode()) },
    onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction
  )
}

@Composable
private fun ToolsContent(
  onImportFilePick: (File) -> Unit,
  onWordToPdfPick: (Uri) -> Unit,
  onScanToPdfComplete: (Uri) -> Unit,
  onImagesPickComplete: (List<Uri>) -> Unit,
  onSettingsClick: () -> Unit,
  onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction,
) {
  val strings = LocalStrings.current

  Scaffold(
    topBar = {
      TopAppBar(
        title = {
          Text(
            text = strings.tabTools,
            style = AppTheme.typography.h2,
            color = AppTheme.colors.onPrimary
          )
        },
        colors = TopAppBarDefaults.topAppBarColors(
          containerColor = AppTheme.colors.primary,
        )
      )
    },
    containerColor = AppTheme.colors.secondary.copy(.15f)
  ) {
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(top = it.calculateTopPadding())
        .verticalScroll(rememberScrollState())
    ) {
      BlankSpacer(16.dp)

      FlowRow(
        modifier = Modifier
          .fillMaxWidth()
          .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        maxItemsInEachRow = 2
      ) {
        DocumentFilePickerContainer(
          modifier = Modifier.weight(1f),
          onPickComplete = onImportFilePick
        ) { startPick ->
          ToolItem(
            onClick = {
              onTryToShowInterAdAndNavAction {
                skipSplash()
                startPick()
              }
            },
            painter = painterResource(R.drawable.img_tool_import_file),
            title = strings.toolImportFile,
            modifier = Modifier
              .weight(1f)
              .fillMaxWidth()
          )
        }

        AdaptiveImageUriPickerContainer(
          modifier = Modifier.weight(1f),
          onPickComplete = onImagesPickComplete
        ) { startPick ->
          ToolItem(
            onClick = {
              onTryToShowInterAdAndNavAction {
                startPick()
              }
            },
            painter = painterResource(R.drawable.img_tool_img_to_pdf),
            title = strings.toolImagesToPdf,
            modifier = Modifier
              .weight(1f)
              .fillMaxWidth()
          )
        }

        DocumentUriPickerContainer(
          modifier = Modifier.weight(1f),
          documentMimeTypes = ImportDocumentFilesHelper.WORD_MIME_TYPE,
          onPickComplete = { wordUri ->
            wordUri?.let(onWordToPdfPick)
          }
        ) { startPick ->
          ToolItem(
            onClick = {
              onTryToShowInterAdAndNavAction {
                skipSplash()
                startPick()
              }
            },
            painter = painterResource(R.drawable.img_tool_word_to_pdf),
            title = strings.toolWordToPdf,
            modifier = Modifier
              .weight(1f)
              .fillMaxWidth()
          )
        }

        DocumentScannerContainer(
          modifier = Modifier.weight(1f),
          onScanComplete = { scannedUris ->
            scannedUris.firstOrNull()?.let(onScanToPdfComplete)
          }
        ) { startScan ->
          ToolItem(
            onClick = {
              onTryToShowInterAdAndNavAction {
                skipSplash()
                startScan()
              }
            },
            painter = painterResource(R.drawable.img_tool_scan_to_pdf),
            title = strings.toolScanToPdf,
            modifier = Modifier
              .weight(1f)
              .fillMaxWidth()
          )
        }
      }

      BlankSpacer(16.dp)

      NativeAd(adPlace = NativeAdPlace.Tools)

      BlankSpacer(16.dp)

      FlowRow(
        modifier = Modifier
          .fillMaxWidth()
          .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        maxItemsInEachRow = 2
      ) {
        ToolItem(
          onClick = {
            onTryToShowInterAdAndNavAction {
              push(LockPdfPickerNode())
            }
          },
          painter = painterResource(R.drawable.img_tool_pdf_lock),
          title = strings.toolLockPdf,
          modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        )

        ToolItem(
          onClick = {
            onTryToShowInterAdAndNavAction {
              push(UnlockPdfPickerNode())
            }
          },
          painter = painterResource(R.drawable.img_tool_pdf_unlock),
          title = strings.toolUnlockPdf,
          modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        )

        ToolItem(
          onClick = {
            onTryToShowInterAdAndNavAction {
              push(AnnotatePdfPickerNode())
            }
          },
          painter = painterResource(R.drawable.img_tool_pdf_annotate),
          title = strings.toolAnnotate,
          modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        )

        ToolItem(
          onClick = {
            onTryToShowInterAdAndNavAction {
              push(AddSignatureToPdfPickerNode())
            }
          },
          painter = painterResource(R.drawable.img_tool_pdf_signature),
          title = strings.toolSignature,
          modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        )
      }

      BlankSpacer(16.dp)

      NotificationItem(modifier = Modifier.padding(horizontal = 16.dp))

      BlankSpacer(16.dp)

      SettingsItem(
        icon = painterResource(R.drawable.ic_settings),
        title = strings.textSettings,
        onClick = onSettingsClick,
        modifier = Modifier.padding(horizontal = 16.dp)
      )

      BlankSpacer(16.dp)
    }
  }
}

@Composable
fun ToolItem(
  onClick: () -> Unit,
  painter: Painter,
  title: String,
  modifier: Modifier = Modifier
) {
  Card(
    onClick = onClick,
    modifier = modifier,
  ) {
    Column(
      modifier = Modifier.padding(12.dp),
      horizontalAlignment = Alignment.Start,
      verticalArrangement = Arrangement.Center
    ) {

      Image(
        painter = painter,
        contentDescription = title,
        modifier = Modifier.size(48.dp)
      )

      Spacer(modifier = Modifier.height(12.dp))
      Text(
        text = title,
        style = AppTheme.typography.h3,
      )
    }
  }
}

@Preview(showBackground = true)
@Composable
private fun ToolItemPreview() {
  AppTheme {
    ToolItem(
      onClick = {},
      painter = painterResource(R.drawable.img_tool_import_file),
      title = strings.sampleTool
    )
  }
}
