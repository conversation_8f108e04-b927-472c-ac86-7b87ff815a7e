package com.example.pdf.ui.node.convert_success

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.pdf.R
import com.example.pdf.android.context.findActivity
import com.example.pdf.android.share.shareFile
import com.example.pdf.appActivity
import com.example.pdf.biz.ad.AdLoadingDialog
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.AdmobInterstitialAdSideEffect
import com.example.pdf.biz.ad.interstitial.AdmobInterstitialAdViewModel
import com.example.pdf.biz.ad.interstitial.MaxInterstitialAdHelper
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.biz.remoteconfig.useLegacyAdConfig
import com.example.pdf.guia.NavigateAction
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.lumo.grayTint7f
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.document_item.formatDate
import com.example.pdf.ui.feature.document_item.formatFileSize
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.io.File

enum class SuccessfulNodeType(val tag: String) {
  Convert("convert"),
  Scan("scan"),
  LockPdf("lock_pdf"),
  UnlockPdf("unlock_pdf"),
}

fun ConvertSuccessNode(document: File) = SuccessfulNode(SuccessfulNodeType.Convert, document)
fun ScanSuccessNode(document: File) = SuccessfulNode(SuccessfulNodeType.Scan, document)
fun LockPdfSuccessNode(document: File) = SuccessfulNode(SuccessfulNodeType.LockPdf, document)
fun UnlockPdfSuccessNode(document: File) = SuccessfulNode(SuccessfulNodeType.UnlockPdf, document)

@Parcelize
class SuccessfulNode(
  private val type: SuccessfulNodeType,
  private val document: File
) : ScreenNode("${type.tag}_success") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    LaunchedEffect(Unit) {
      RatingHelper.setCanRatting(true)
    }

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    ConvertSuccessContent(
      type = type,
      document = document,
      onBack = onBackAction,
      onOpenFile = { _ ->
        DocumentNode(document)?.let {
          onTryToShowInterAdAndNavAction { replaceLast(it) }
        }
      },
      onShareFile = { appActivity?.shareFile(it) }
    )
  }
}

@Composable
private fun ConvertSuccessContent(
  type: SuccessfulNodeType,
  document: File,
  onBack: () -> Unit,
  onOpenFile: (File) -> Unit,
  onShareFile: (File) -> Unit
) {
  val strings = LocalStrings.current
  Scaffold(
    topBar = {
      Column {
        TopBar(
          colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
        ) {
          Row(
            modifier = Modifier
              .fillMaxWidth()
              .padding(horizontal = 8.dp)
          ) {
            IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
              Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
            }
          }
        }
        BannerAd(adPlace = BannerAdPlace.ConvertSuccess)
      }
    },
    containerColor = AppTheme.colors.disabled.copy(.15f)
  ) { paddingValues ->
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      Spacer(modifier = Modifier.weight(1f))

      Icon(
        imageVector = Icons.Rounded.CheckCircle,
        tint = AppTheme.colors.primary,

        modifier = Modifier.size(80.dp)
      )

      Spacer(modifier = Modifier.height(16.dp))

      // Success Text
      val successText = remember(type.tag) {
        when (type) {
          SuccessfulNodeType.Convert -> strings.statusConvertedSuccessfully
          SuccessfulNodeType.Scan -> strings.statusScannedSuccessfully
          SuccessfulNodeType.LockPdf -> strings.statusLockedSuccessfully
          SuccessfulNodeType.UnlockPdf -> strings.statusUnlockedSuccessfully
        }
      }

      Text(
        text = successText,
        style = AppTheme.typography.h1,
        fontWeight = FontWeight.Bold
      )

      Spacer(modifier = Modifier.height(32.dp))

      // File Info Card
      Card(
        modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp),
      ) {
        Column(modifier = Modifier.padding(16.dp)) {
          Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
          ) {
            Icon(
              painter = painterResource(R.drawable.ic_pdf),
              tint = AppTheme.colors.primary,
              modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.width(10.dp))
            Column {
              Text(
                text = document.name,
                style = AppTheme.typography.h3,
                fontWeight = FontWeight.Medium,
                maxLines = 1
              )
              Spacer(modifier = Modifier.height(1.dp))
              Text(
                text = "${formatFileSize(document.length())} | ${formatDate(document.lastModified())}",
                style = AppTheme.typography.body2,
                color = grayTint7f
              )
            }
          }

          BlankSpacer(16.dp)

          Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
          ) {
            // Open Button
            Button(
              onClick = { onOpenFile(document) },
              modifier = Modifier
                .weight(1f)
                .height(48.dp),
              variant = ButtonVariant.SecondaryLight
            ) {
              Row {
                Image(
                  painter = painterResource(id = R.drawable.ic_open),
                  contentDescription = strings.contentDescriptionOpen,
                  modifier = Modifier.size(20.dp),
                )

                Text(
                  text = strings.textOpen,
                  fontWeight = FontWeight.Medium,
                  modifier = Modifier.padding(horizontal = 8.dp)
                )
              }

            }

            Spacer(modifier = Modifier.width(16.dp))

            // Share Button
            Button(
              onClick = { onShareFile(document) },
              modifier = Modifier
                .weight(1f)
                .height(48.dp),
              variant = ButtonVariant.Primary
            ) {
              Row {
                Image(
                  painter = painterResource(id = R.drawable.ic_share),
                  contentDescription = strings.contentDescriptionShare,
                  modifier = Modifier.size(20.dp),
                  colorFilter = ColorFilter.tint(AppTheme.colors.onPrimary)
                )

                Text(
                  text = strings.textShare,
                  fontWeight = FontWeight.Medium,
                  modifier = Modifier.padding(horizontal = 8.dp)
                )
              }
            }
          }
        }


      }

      Spacer(modifier = Modifier.height(16.dp))

      NativeAd(adPlace = NativeAdPlace.ConvertSuccess)

      Spacer(modifier = Modifier.height(32.dp))

      Spacer(Modifier.weight(3f))
    }
  }
}

@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
private fun ConvertSuccessContentPreview() {
  // Create a dummy file for preview purposes
  // Note: In a real scenario, you might need a more robust way
  // to handle file creation/mocking for previews if the file system
  // access is restricted or causes issues.
  val previewFile = File("converted_example.pdf").apply {
    // Set some mock properties if needed, though accessing them directly
    // without the file existing might not work as expected in all preview environments.
    // For display, relying on default values or formatters handling non-existent files might be sufficient.
  }

  // Using AppTheme to ensure the preview uses the correct styling
  AppTheme {
    ConvertSuccessContent(
      type = SuccessfulNodeType.Convert,
      document = previewFile,
      onBack = {},
      onOpenFile = {},
      onShareFile = {}
    )
  }
}