package com.example.pdf.lyricist

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.text.intl.Locale
import cafe.adriel.lyricist.LanguageTag
import cafe.adriel.lyricist.LocalStrings
import cafe.adriel.lyricist.Lyricist
import cafe.adriel.lyricist.Strings
import com.example.pdf.lyricist.i18n.StringsEn
import kotlinx.coroutines.flow.MutableStateFlow

object Locales {
  const val EN = "en"
}

val runtimeLanguageTagFlow = MutableStateFlow(Locale.current.toLanguageTag())

@Composable
fun rememberStrings(
  defaultLanguageTag: LanguageTag = Locales.EN,
  currentLanguageTag: LanguageTag = Locale.current.toLanguageTag(),
): Lyricist<Strings> =
  cafe.adriel.lyricist.rememberStrings(Strings, defaultLanguageTag, currentLanguageTag)

@Composable
fun ProvideStrings(
  lyricist: Lyricist<Strings> = rememberStrings(),
  content: @Composable () -> Unit,
) {
  cafe.adriel.lyricist.ProvideStrings(lyricist, LocalStrings, content)
}

private var _globalStrings: Strings? = null

fun configureGlobalStrings(strings: Strings) {
  _globalStrings = strings
}

@Composable
fun ConfigureGlobalStringsEffect(strings: Strings) {
  LaunchedEffect(strings) {
    configureGlobalStrings(strings)
  }
}

val globalStrings get() = _globalStrings ?: StringsEn

data class Strings(
  // Common Actions
  val textOK: String,
  val textCancel: String,
  val textSave: String,
  val textDelete: String,
  val textShare: String,
  val textOpen: String,
  val textConvert: String,
  val textGrant: String,
  val textAccept: String,
  val textDecline: String,
  val textSettings: String,
  val textFeedback: String,
  val textPrivacyPolicy: String,
  val textVersion: String,

  // Navigation Tabs
  val tabAll: String,
  val tabRecent: String,
  val tabBookmarks: String,
  val tabTools: String,

  // Tool Names
  val toolScanToPdf: String,
  val toolLockPdf: String,
  val toolUnlockPdf: String,
  val toolAnnotate: String,
  val toolSignature: String,
  val toolImportFile: String,
  val toolWordToPdf: String,
  val toolImagesToPdf: String,

  // Status Messages
  val statusLoading: String,
  val statusConvertedSuccessfully: String,
  val statusScannedSuccessfully: String,
  val statusLockedSuccessfully: String,
  val statusUnlockedSuccessfully: String,

  // Placeholder Text
  val placeholderSearch: String,
  val placeholderNoFilesFound: String,
  val placeholderNoBookmarks: String,

  // Dialog Titles
  val dialogPermissionRequired: String,
  val dialogTermsConditions: String,
  val dialogSimpleAlert: String,

  // Permission Messages
  val permissionFileAccessTips: String,
  val permissionFileAccessTipsShort: String,
  val permissionFileAccessTipsLegacy: String,

  // Content Descriptions
  val contentDescriptionSearch: String,
  val contentDescriptionBookmarks: String,
  val contentDescriptionTools: String,
  val contentDescriptionNoFiles: String,
  val contentDescriptionNoBookmarks: String,
  val contentDescriptionAppIcon: String,
  val contentDescriptionOpen: String,
  val contentDescriptionShare: String,
  val contentDescriptionDismissTips: String,

  // Convert Actions
  val convertToPdf: String,
  val convertWithCount: String,

  // Long Content Example
  val longContentExample: String,

  // Misc
  val dialogDescription: String,
  val sampleTool: String,
  val longPressDragReorder: String,
  val importDocument: String,
  val selectFile: String
)